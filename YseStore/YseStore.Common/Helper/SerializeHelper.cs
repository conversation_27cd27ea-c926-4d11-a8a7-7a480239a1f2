using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common
{


    public static class JsonUtil
    {

      
        private static JsonSerializerSettings options = new JsonSerializerSettings
        {
            DateFormatString = "yyyy-MM-dd HH:mm:ss"
        };

        public static string ToJson(this object obj, bool allowCircularRef = false)
        {
            string text = "{}";
            if (allowCircularRef)
            {
                JsonSerializerSettings settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    DateFormatString = "yyyy-MM-dd HH:mm:ss"
                };
                return JsonConvert.SerializeObject(obj, settings);
            }

            return JsonConvert.SerializeObject(obj, options);
        }

        /// <summary>
        /// 用于日志输出的JSON序列化，格式化输出且避免不必要的字符转义
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>格式化的JSON字符串</returns>
        public static string ToJsonForLog(this object obj)
        {
            var logSettings = new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                DateFormatString = "yyyy-MM-dd HH:mm:ss",
                StringEscapeHandling = StringEscapeHandling.Default,
                NullValueHandling = NullValueHandling.Ignore,
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };

            return JsonConvert.SerializeObject(obj, logSettings);
        }

        public static T JsonToObj<T>(this string json, bool allowCircularRef = false)
        {
            T val = default(T);
            if (allowCircularRef)
            {
                JsonSerializerSettings settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    DateFormatString = "yyyy-MM-dd HH:mm:ss"
                };
                return JsonConvert.DeserializeObject<T>(json, settings);
            }

            return JsonConvert.DeserializeObject<T>(json, options);
        }
    }

}
